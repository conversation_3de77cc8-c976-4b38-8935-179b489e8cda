## 12.4.9

 - Update a dependency to the latest release.

## 12.4.8

 - **FIX**(core): bump Pigeon to v25.3.2 ([#17438](https://github.com/firebase/flutterfire/issues/17438)). ([4d24ef53](https://github.com/firebase/flutterfire/commit/4d24ef534464b39dcaef4151c83c78f87b36fb78))

## 12.4.7

 - Update a dependency to the latest release.

## 12.4.6

 - Update a dependency to the latest release.

## 12.4.5

 - Update a dependency to the latest release.

## 12.4.4

 - Update a dependency to the latest release.

## 12.4.3

 - Update a dependency to the latest release.

## 12.4.2

 - Update a dependency to the latest release.

## 12.4.1

 - **FIX**(storage,android): fix an issue that could crash the app when concurrent calls to removeEventListeners were happening ([#16996](https://github.com/firebase/flutterfire/issues/16996)). ([6499c5f5](https://github.com/firebase/flutterfire/commit/6499c5f5457bca168e6934679562548a94e4f7a8))

## 12.4.0

 - **FIX**(storage): update regex for cloudStoragePath ([#16847](https://github.com/firebase/flutterfire/issues/16847)). ([b0832175](https://github.com/firebase/flutterfire/commit/b08321754c1fc8b773c9ea61c2e09fe866cefacc))
 - **FEAT**(storage): Swift Package Manager support ([#16782](https://github.com/firebase/flutterfire/issues/16782)). ([b5993aef](https://github.com/firebase/flutterfire/commit/b5993aef0bf12d056a366bea9c7ce51c9781e290))

## 12.3.7

 - **FIX**(storage,apple): clean up event channel, stream handler and task on completion ([#16708](https://github.com/firebase/flutterfire/issues/16708)). ([14b4a552](https://github.com/firebase/flutterfire/commit/14b4a552f90ea03b297938ee30423c0e1e7d888e))

## 12.3.6

 - Update a dependency to the latest release.

## 12.3.5

 - Update a dependency to the latest release.

## 12.3.4

 - **FIX**(storage,android): stream handler & event channel clean up on completion ([#13508](https://github.com/firebase/flutterfire/issues/13508)). ([f010b468](https://github.com/firebase/flutterfire/commit/f010b4684e38f47ad9b38d34c3a84a4eb4518fac))

## 12.3.3

 - **FIX**(storage,web): fix putData when using UInt8List ([#13466](https://github.com/firebase/flutterfire/issues/13466)). ([2bfb549e](https://github.com/firebase/flutterfire/commit/2bfb549ee6706648a0bf661781195171cfb05cb5))

## 12.3.2

 - **FIX**(all,apple): use modular headers to import ([#13400](https://github.com/firebase/flutterfire/issues/13400)). ([d7d2d4b9](https://github.com/firebase/flutterfire/commit/d7d2d4b93e7c00226027fffde46699f3d5388a41))

## 12.3.1

 - Update a dependency to the latest release.

## 12.3.0

 - **FEAT**(web): update to `web: ^1.0.0` ([#13200](https://github.com/firebase/flutterfire/issues/13200)). ([8fab04ae](https://github.com/firebase/flutterfire/commit/8fab04aec3b95789856d95639131bf09db29175b))

## 12.2.0

 - **FEAT**: bump iOS SDK to version 11.0.0 ([#13158](https://github.com/firebase/flutterfire/issues/13158)). ([c0e0c997](https://github.com/firebase/flutterfire/commit/c0e0c99703ea394d1bb873ac225c5fe3539b002d))
 - **DOCS**: remove reference to flutter.io and firebase.flutter.dev ([#13152](https://github.com/firebase/flutterfire/issues/13152)). ([5f0874b9](https://github.com/firebase/flutterfire/commit/5f0874b91e28a203dd62d37d391e5760c91f5729))

## 12.1.3

 - **FIX**(storage,windows): add log to explain that the Storage Emulator is not available on Windows ([#13147](https://github.com/firebase/flutterfire/issues/13147)). ([8d1ea80c](https://github.com/firebase/flutterfire/commit/8d1ea80cf7b007459572405c876e813b43c3b4cf))

## 12.1.2

 - Update a dependency to the latest release.

## 12.1.1

 - Update a dependency to the latest release.

## 12.1.0

 - **FEAT**(storage,windows): add support for creationTime and updateTime ([#12966](https://github.com/firebase/flutterfire/issues/12966)). ([70a3fbc9](https://github.com/firebase/flutterfire/commit/70a3fbc97ec34c811616e92333aae299dd2ef569))

## 12.0.1

 - Update a dependency to the latest release.

## 12.0.0

> Note: This release has breaking changes.

 - **BREAKING** **REFACTOR**: android plugins require `minSdk 21`, auth requires `minSdk 23` ahead of android BOM `>=33.0.0` ([#12873](https://github.com/firebase/flutterfire/issues/12873)). ([52accfc6](https://github.com/firebase/flutterfire/commit/52accfc6c39d6360d9c0f36efe369ede990b7362))
 - **BREAKING** **REFACTOR**: bump all iOS deployment targets to iOS 13 ahead of Firebase iOS SDK `v11` breaking change ([#12872](https://github.com/firebase/flutterfire/issues/12872)). ([de0cea2c](https://github.com/firebase/flutterfire/commit/de0cea2c3c36694a76361be784255986fac84a43))
 - **BREAKING** **REFACTOR**(storage): remove deprecated API ahead of breaking change ([#12863](https://github.com/firebase/flutterfire/issues/12863)). ([aaf01ac5](https://github.com/firebase/flutterfire/commit/aaf01ac5488e4d599f42f361f9a51f1297dce7c3))

## 11.7.7

 - **FIX**(storage): do not set metadata property unless it has a value ([#12805](https://github.com/firebase/flutterfire/issues/12805)). ([978a87db](https://github.com/firebase/flutterfire/commit/978a87db70218d63abbf0c6bf22e9d00633b5d83))

## 11.7.6

 - **FIX**(storage,windows): putFile(), putString(), putData() & Task streaming event fixes ([#12723](https://github.com/firebase/flutterfire/issues/12723)). ([de69e07a](https://github.com/firebase/flutterfire/commit/de69e07a36a9e2ce967d9f4470f4a14e987abf53))

## 11.7.5

 - Update a dependency to the latest release.

## 11.7.4

 - Update a dependency to the latest release.

## 11.7.3

 - **FIX**(storage): pass StorageMetadata as nullable for `putFile()` API. Metadata ought to be inferred from File. ([#12612](https://github.com/firebase/flutterfire/issues/12612)). ([e75d134f](https://github.com/firebase/flutterfire/commit/e75d134faef2cbf156d8936fa3f1c8c69cd59ec2))

## 11.7.2

 - Update a dependency to the latest release.

## 11.7.1

 - **FIX**(web): remove remaining references to `dart:html` ([#12601](https://github.com/firebase/flutterfire/issues/12601)). ([fbffedb6](https://github.com/firebase/flutterfire/commit/fbffedb65c180d29a07436af90b36ca56e97bfe7))

## 11.7.0

 - **FEAT**(android): Bump `compileSdk` version of Android plugins to latest stable (34) ([#12566](https://github.com/firebase/flutterfire/issues/12566)). ([e891fab2](https://github.com/firebase/flutterfire/commit/e891fab291e9beebc223000b133a6097e066a7fc))

## 11.6.11

 - Update a dependency to the latest release.

## 11.6.10

 - Update a dependency to the latest release.

## 11.6.9

 - Update a dependency to the latest release.

## 11.6.8

 - Update a dependency to the latest release.

## 11.6.7

 - **FIX**(storage): `Task.cancel()` method wasn't properly updating `task.snapshot` and `cancel()` wasn't working in certain conditions. ([#12322](https://github.com/firebase/flutterfire/issues/12322)). ([c3ca5d10](https://github.com/firebase/flutterfire/commit/c3ca5d101b735e2203ca3d4e52f0b457794b3a47))

## 11.6.6

 - Update a dependency to the latest release.

## 11.6.5

 - Update a dependency to the latest release.

## 11.6.4

 - Update a dependency to the latest release.

## 11.6.3

 - Update a dependency to the latest release.

## 11.6.2

 - Update a dependency to the latest release.

## 11.6.1

 - **FIX**(storage,windows): `getData()` crash has been fixed ([#12185](https://github.com/firebase/flutterfire/issues/12185)). ([ed8c7c51](https://github.com/firebase/flutterfire/commit/ed8c7c51d28398b1f68af404bdd855b75a1b1f56))
 - **FIX**(storage): ensure Task listeners correctly propagate exceptions and close properly. ([#12160](https://github.com/firebase/flutterfire/issues/12160)). ([759684b1](https://github.com/firebase/flutterfire/commit/759684b1b445bf238e9644ef1dc495cdc6a55dd8))
 - **FIX**(storage,android): fix `refFromUrl()` when using firebase storage emulator. Essentially, check `********` domain as well. ([#12047](https://github.com/firebase/flutterfire/issues/12047)). ([cef006a6](https://github.com/firebase/flutterfire/commit/cef006a69bafeae1d3481220e2a5fb7386bdfbe3))
 - **DOCS**: change old documentation links of packages in README files ([#12136](https://github.com/firebase/flutterfire/issues/12136)). ([24b9ac7e](https://github.com/firebase/flutterfire/commit/24b9ac7ec29fc9ca466c0941c2cff26d75b8568d))

## 11.6.0

 - **FEAT**: allow users to disable automatic host mapping ([#11962](https://github.com/firebase/flutterfire/issues/11962)). ([13c1ce33](https://github.com/firebase/flutterfire/commit/13c1ce333b8cd113241a1f7ac07181c1c76194bc))

## 11.5.6

 - **FIX**(storage,windows): fix an issue where getData function would statically allocate memory ([#12020](https://github.com/firebase/flutterfire/issues/12020)). ([7d51dbdc](https://github.com/firebase/flutterfire/commit/7d51dbdc1795ff9708dc3e60e4fb7089e0af8d36))

## 11.5.5

 - Update a dependency to the latest release.

## 11.5.4

 - Update a dependency to the latest release.

## 11.5.3

 - Update a dependency to the latest release.

## 11.5.2

 - Update a dependency to the latest release.

## 11.5.1

 - **FIX**(storage,apple): set the storage emulator only once to stop it from crashing on hot restart ([#11862](https://github.com/firebase/flutterfire/issues/11862)). ([7f07d7aa](https://github.com/firebase/flutterfire/commit/7f07d7aaf3e4c978b7404660f736032b90bffd61))

## 11.5.0

 - **FIX**(storage): ensure bucket value is used to create FirebaseStorage instance to stop incorrect default bucket usage ([#11844](https://github.com/firebase/flutterfire/issues/11844)). ([49542f32](https://github.com/firebase/flutterfire/commit/49542f32ca8ae1eef6065e40ddb21fa40e66d7f0))
 - **FEAT**(windows): add platform logging for core, auth, firestore and storage ([#11790](https://github.com/firebase/flutterfire/issues/11790)). ([e7d428d1](https://github.com/firebase/flutterfire/commit/e7d428d14be1535a2d579d4b2d376fbb81f06742))

## 11.4.1

 - Update a dependency to the latest release.

## 11.4.0

 - **FEAT**(storage,windows): Add windows support ([#11617](https://github.com/firebase/flutterfire/issues/11617)). ([87ea02c8](https://github.com/firebase/flutterfire/commit/87ea02c8ae03eb351636cf202961ad0df6caebd8))
 - **FEAT**(storage): move Storage to use Pigeon for platform channels ([#11521](https://github.com/firebase/flutterfire/issues/11521)). ([edddc1de](https://github.com/firebase/flutterfire/commit/edddc1def508d0c516534b80c13d41a919fd39bc))

## 11.3.1

 - Update a dependency to the latest release.

## 11.3.0

 - **FEAT**: Full support of AGP 8 ([#11699](https://github.com/firebase/flutterfire/issues/11699)). ([bdb5b270](https://github.com/firebase/flutterfire/commit/bdb5b27084d225809883bdaa6aa5954650551927))

## 11.2.8

 - Update a dependency to the latest release.

## 11.2.7

 - Update a dependency to the latest release.

## 11.2.6

 - Update a dependency to the latest release.

## 11.2.5

 - Update a dependency to the latest release.

## 11.2.4

 - Update a dependency to the latest release.

## 11.2.3

 - Update a dependency to the latest release.

## 11.2.2

 - Update a dependency to the latest release.

## 11.2.1

 - Update a dependency to the latest release.

## 11.2.0

 - **FEAT**: update dependency constraints to `sdk: '>=2.18.0 <4.0.0'` `flutter: '>=3.3.0'` ([#10946](https://github.com/firebase/flutterfire/issues/10946)). ([2772d10f](https://github.com/firebase/flutterfire/commit/2772d10fe510dcc28ec2d37a26b266c935699fa6))

## 11.1.2

 - **FIX**: add support for AGP 8.0 ([#10901](https://github.com/firebase/flutterfire/issues/10901)). ([a3b96735](https://github.com/firebase/flutterfire/commit/a3b967354294c295a9be8d699a6adb7f4b1dba7f))

## 11.1.1

 - Update a dependency to the latest release.

## 11.1.0

 - **FEAT**: bump dart sdk constraint to 2.18 ([#10618](https://github.com/firebase/flutterfire/issues/10618)). ([f80948a2](https://github.com/firebase/flutterfire/commit/f80948a28b62eead358bdb900d5a0dfb97cebb33))

## 11.0.16

 - Update a dependency to the latest release.

## 11.0.15

 - Update a dependency to the latest release.

## 11.0.14

 - Update a dependency to the latest release.

## 11.0.13

 - Update a dependency to the latest release.

## 11.0.12

 - Update a dependency to the latest release.

## 11.0.11

 - **REFACTOR**: upgrade project to remove warnings from Flutter 3.7 ([#10344](https://github.com/firebase/flutterfire/issues/10344)). ([e0087c84](https://github.com/firebase/flutterfire/commit/e0087c845c7526c11a4241a26d39d4673b0ad29d))

## 11.0.10

 - Update a dependency to the latest release.

## 11.0.9

 - Update a dependency to the latest release.

## 11.0.8

 - **FIX**: fix usage of storage plugin with an isolate ([#10106](https://github.com/firebase/flutterfire/issues/10106)). ([86f24633](https://github.com/firebase/flutterfire/commit/86f2463364cbd3a0de88b1e6217120a95609b5b2))
 - **FIX**: fix concurrency issue ([#10099](https://github.com/firebase/flutterfire/issues/10099)). ([d0b6fcf1](https://github.com/firebase/flutterfire/commit/d0b6fcf194afe987dec59d24b47ad197bf8e7cf8))

## 11.0.7

 - Update a dependency to the latest release.

## 11.0.6

 - Update a dependency to the latest release.

## 11.0.5

 - Update a dependency to the latest release.

## 11.0.4

 - Update a dependency to the latest release.

## 11.0.3

 - **REFACTOR**: add `verify` to `QueryPlatform` and change internal `verifyToken` API to `verify` ([#9711](https://github.com/firebase/flutterfire/issues/9711)). ([c99a842f](https://github.com/firebase/flutterfire/commit/c99a842f3e3f5f10246e73f51530cc58c42b49a3))

## 11.0.2

 - Update a dependency to the latest release.

## 11.0.1

 - Update a dependency to the latest release.

## 11.0.0

> Note: This release has breaking changes.

 - **BREAKING** **FEAT**: Firebase iOS SDK version: `10.0.0` ([#9708](https://github.com/firebase/flutterfire/issues/9708)). ([9627c56a](https://github.com/firebase/flutterfire/commit/9627c56a37d657d0250b6f6b87d0fec1c31d4ba3))

## 10.3.11

 - Update a dependency to the latest release.

## 10.3.10

 - Update a dependency to the latest release.

## 10.3.9

 - Update a dependency to the latest release.

## 10.3.8

 - Update a dependency to the latest release.

## 10.3.7

 - Update a dependency to the latest release.

## 10.3.6

 - Update a dependency to the latest release.

## 10.3.5

 - Update a dependency to the latest release.

## 10.3.4

 - Update a dependency to the latest release.

## 10.3.3

 - Update a dependency to the latest release.

## 10.3.2

 - Update a dependency to the latest release.

## 10.3.1

 - **FIX**: bump `firebase_core_platform_interface` version to fix previous release. ([bea70ea5](https://github.com/firebase/flutterfire/commit/bea70ea5cbbb62cbfd2a7a74ae3a07cb12b3ee5a))

## 10.3.0

 - **FEAT**: Bump Firebase iOS SDK to `9.2.0` (#8594). ([79610162](https://github.com/firebase/flutterfire/commit/79610162460b8877f3bc727464a7065106f08079))

## 10.2.18

 - **REFACTOR**: migrate from hash* to Object.hash* (#8797). ([3dfc0997](https://github.com/firebase/flutterfire/commit/3dfc0997050ee4351207c355b2c22b46885f971f))
 - **REFACTOR**: use "firebase" instead of "FirebaseExtended" as organisation in all links for this repository (#8791). ([d90b8357](https://github.com/firebase/flutterfire/commit/d90b8357db01d65e753021358668f0b129713e6b))
 - **DOCS**: point to "firebase.google" domain for hyperlinks in the usage section of `README.md` files (#8814). ([78006e0d](https://github.com/firebase/flutterfire/commit/78006e0d5b9dce8038ce3606a43ddcbc8a4a71b9))

## 10.2.17

 - **DOCS**: use camel case style for "FlutterFire" in `README.md` (#8752). ([5c5dcaf1](https://github.com/firebase/flutterfire/commit/5c5dcaf1909dacf293fec5e79461d43468a13279))

## 10.2.16

 - Update a dependency to the latest release.

## 10.2.15

 - Update a dependency to the latest release.

## 10.2.14

 - **REFACTOR**: Remove deprecated `Tasks.call()` API from android. (#8421). ([461bba5a](https://github.com/firebase/flutterfire/commit/461bba5a510b341b3b9bd414c9412944714e9305))

## 10.2.13

 - Update a dependency to the latest release.

## 10.2.12

 - Update a dependency to the latest release.

## 10.2.11

 - **FIX**: Fix `UploadTask.cancel()` so that it completes when called. (#8417). ([19ee62c3](https://github.com/firebase/flutterfire/commit/19ee62c33f34278dac082c11bf7574785e60abb5))

## 10.2.10

 - Update a dependency to the latest release.

## 10.2.9

 - **FIX**: update all Dart SDK version constraints to Dart >= 2.16.0 (#8184). ([df4a5bab](https://github.com/firebase/flutterfire/commit/df4a5bab3c029399b4f257a5dd658d302efe3908))

## 10.2.8

 - Update a dependency to the latest release.

## 10.2.7

- Update a dependency to the latest release.

## 10.2.6

 - Update a dependency to the latest release.

## 10.2.5

 - **FIX**: bump Android `compileSdkVersion` to 31 (#7726). ([a9562bac](https://github.com/firebase/flutterfire/commit/a9562bac60ba927fb3664a47a7f7eaceb277dca6))

## 10.2.4

 - **REFACTOR**: fix all `unnecessary_import` analyzer issues introduced with Flutter 2.8. ([7f0e82c9](https://github.com/firebase/flutterfire/commit/7f0e82c978a3f5a707dd95c7e9136a3e106ff75e))

## 10.2.3

 - Update a dependency to the latest release.

## 10.2.2

 - Update a dependency to the latest release.

## 10.2.1

 - Update a dependency to the latest release.

## 10.2.0

 - **REFACTOR**: migrate remaining examples & e2e tests to null-safety (#7393).
 - **FEAT**: automatically inject Firebase JS SDKs (#7359).

## 10.1.0

 - **FEAT**: support initializing default `FirebaseApp` instances from Dart (#6549).

## 10.0.7

 - Update a dependency to the latest release.

## 10.0.6

 - **REFACTOR**: remove deprecated Flutter Android v1 Embedding usages, including in example app (#7158).
 - **STYLE**: macOS & iOS; explicitly include header that defines `TARGET_OS_OSX` (#7116).

## 10.0.5

 - **FIX**: remove https port number from `downloadUrl` for `iOS` (#7097).

## 10.0.4

 - **FIX**: fix localhost url parsing for Storage Emulator (#7003).
 - **CHORE**: update gradle version across packages (#7054).

## 10.0.3

 - **FIX**: accept google storage bucket urls (#6848).

## 10.0.2

 - Update a dependency to the latest release.

## 10.0.1

 - **FIX**: reinstate deprecated emulator apis (#6626).

## 10.0.0

> Note: This release has breaking changes.

 - **FIX**: Use `mappedHost` instead of `host` (#6539).
 - **CHORE**: update v2 embedding support (#6506).
 - **CHORE**: rm deprecated jcenter repository (#6431).
 - **BREAKING** **FEAT**: use<product>Emulator(host, port) API update.
 - **BREAKING** **FEAT**: use<product>Emulator(host, port) API update (#6439).

## 9.0.0

> Note: This release has breaking changes.

 - **CHORE**: rm deprecated jcenter repository (#6431).
 - **BREAKING** **FEAT**: useStorageEmulator(host, port) API update.

## 8.1.3

 - Update a dependency to the latest release.

## 8.1.2

 - **FIX**: refFromUrl parse url (#6353).

## 8.1.1

 - **DOCS**: Add Flutter Favorite badge (#6190).

## 8.1.0

 - **FEAT**: add support for the Firebase Storage emulator via `useEmulator` (#5936).
 - **FEAT**: upgrade Firebase JS SDK version to 8.6.1.
 - **FIX**: podspec osx version checking script should use a version range instead of a single fixed version.

## 8.0.6

 - Update a dependency to the latest release.

## 8.0.5

 - **FIX**: secondary storage buckets now work as expected on Web (#5863).

## 8.0.4

 - Update a dependency to the latest release.

## 8.0.3

 - **FIX**: improve refFromURL http regex (#5682).

## 8.0.2

 - Update a dependency to the latest release.

## 8.0.1

 - **DOCS**: remove incorrect ARCHS in ios examples (#5450).
 - **CHORE**: bump min Dart SDK constraint to 2.12.0 (#5430).
 - **CHORE**: publish packages (#5429).
 - **CHORE**: publish packages.
 - **CHORE**: rm dev dependencies breaking CI (#5221).

## 8.0.0

 - Graduate package to a stable release. See pre-releases prior to this version for changelog entries.

## 8.0.0-1.0.nullsafety.1

 - Update a dependency to the latest release.

## 8.0.0-1.0.nullsafety.0

 - **REFACTOR**: Migrate Firebase Storage to nnbd (#4753).

## 7.0.0

> Note: This release has breaking changes.

 - **FIX**: handle ArrayIndexOutOfBoundsException (fixes #4334) (#4638).
 - **FEAT**: add check on podspec to assist upgrading users deployment target.
 - **BUILD**: commit Podfiles with 10.12 deployment target.
 - **BUILD**: remove default sdk version, version should always come from firebase_core, or be user defined.
 - **BUILD**: set macOS deployment target to 10.12 (from 10.11).
 - **BREAKING** **BUILD**: set osx min supported platform version to 10.12.

## 6.0.0

 - **FEAT**: bump firebase-android-sdk BoM to 25.13.0.
 - **BREAKING** **REFACTOR**: remove all currently deprecated APIs.
 - **BREAKING** **FEAT**: forward port to firebase-ios-sdk v7.3.0.
   - Due to this SDK upgrade, iOS 10 is now the minimum supported version by FlutterFire. Please update your build target version.
 - **CHORE**: harmonize dependencies and version handling.

## 5.2.0

 - **FEAT**: bump android `com.android.tools.build` & `'com.google.gms:google-services` versions (#4269).
 - **CHORE**: Migrate iOS example projects (#4222).

## 5.1.0

 - **TEST**: skip ios pause/resume check.
 - **TEST**: skip ios pause/resume check.
 - **TEST**: Opt-out from null safety. (#4204).
 - **FEAT**: web support (#3917).
 - **FEAT**: Tweaks to firebase_storage for the upcoming web version. (#4028).
 - **CHORE**: bump gradle wrapper to 5.6.4 (#4158).

## 5.0.1

 - Update a dependency to the latest release.

## 5.0.0

 - Graduate package to a stable release. See pre-releases prior to this version for changelog entries.

## 5.0.0-dev.4

 - **FEAT**: bump compileSdkVersion to 29 (#3975).
 - **FEAT**: update Firebase iOS SDK version to 6.33.0 (from 6.26.0).

## 5.0.0-dev.3

 - **FEAT**: rework (#3612).
 - **DOCS**: README updates (#3768).
 - **CHORE**: delete package_config.json (#3744).

## 5.0.0-dev.2

 - **FIX**: if custom metadata value returns null put value as empty string.

## 5.0.0-dev.1

As part of our on-going work for [#2582](https://github.com/firebase/flutterfire/issues/2582) this is our Firebase Storage rework changes.

Overall, Firebase Storage has been heavily reworked to bring it inline with the federated plugin setup along with adding new features,
documentation and many more unit and end-to-end tests (tested on Android, iOS & MacOS).

- **`FirebaseStorage`**

  - **DEPRECATED**: Constructing an instance is now deprecated, use `FirebaseStorage.instanceFor` or `FirebaseStorage.instance` instead.
  - **DEPRECATED**: `getReferenceFromUrl()` is deprecated in favor of calling `ref()` with a path.
  - **DEPRECATED**: `getMaxOperationRetryTimeMillis()` is deprecated in favor of the getter `maxOperationRetryTime`.
  - **DEPRECATED**: `getMaxUploadRetryTimeMillis()` is deprecated in favor of the getter `maxUploadRetryTime`.
  - **DEPRECATED**: `getMaxDownloadRetryTimeMillis()` is deprecated in favor of the getter `maxDownloadRetryTime`.
  - **DEPRECATED**: `setMaxOperationRetryTimeMillis()` is deprecated in favor of `setMaxUploadRetryTime()`.
  - **DEPRECATED**: `setMaxUploadRetryTimeMillis()` is deprecated in favor of `setMaxUploadRetryTime()`.
  - **DEPRECATED**: `setMaxDownloadRetryTimeMillis()` is deprecated in favor of `setMaxDownloadRetryTime()`.
  - **NEW**: To match the Web SDK, calling `ref()` creates a new `Reference` at the bucket root, whereas an optional path (`ref('/foo/bar.png')`) can be used to create a `Reference` pointing at a specific location.
  - **NEW**: Added support for `refFromURL`, which accepts a Google Storage (`gs://`) or HTTP URL and returns a `Reference` synchronously.

- **`Reference`**
  - **BREAKING**: `StorageReference` has been renamed to `Reference`.
  - **DEPRECATED**: `getParent()` is deprecated in favor of `.parent`.
  - **DEPRECATED**: `getRoot()` is deprecated in favor of `.root`.
  - **DEPRECATED**: `getStorage()` is deprecated in favor of `.storage`.
  - **DEPRECATED**: `getBucket()` is deprecated in favor of `.bucket`.
  - **DEPRECATED**: `getPath()` is deprecated in favor of `.fullPath`.
  - **DEPRECATED**: `getName()` is deprecated in favor of `.name`.
  - **NEW**: Added support for `list(options)`.
    - Includes `ListOptions` API (see below).
  - **NEW**: Added support for `listAll()`.
  - **NEW**: `putString()` has been added to accept a string value, of type Base64, Base64Url, a [Data URL](https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URIs) or raw strings.
    - Data URLs automatically set the `Content-Type` metadata if not already set.
  - **NEW**: `getData()` does not require a `maxSize`, it can now be called with a default of 10mb.

- **NEW `ListOptions`**
  - The `list()` method accepts a `ListOptions` instance with the following arguments:
    - `maxResults`: limits the number of results returned from a call. Defaults to 1000.
    - `pageToken`: a page token returned from a `ListResult` - used if there are more items to query.

- **NEW `ListResult`**
  - A `ListResult` class has been added, which is returned from a call to `list()` or `listAll()`. It exposes the following properties:
    - `items` (`List<Reference>`): Returns the list of reference objects at the current reference location.
    - `prefixes` (`List<Reference>`): Returns the list of reference sub-folders at the current reference location.
    - `nextPageToken` (`String`): Returns a string (or null) if a next page during a `list()` call exists.

- **Tasks**
  - Tasks have been overhauled to be closer to the expected Firebase Web SDK Storage API, allowing users to access and control on-going tasks easier. There are a number of breaking changes & features with this overhaul:
    - **BREAKING**: `StorageUploadTask` has been renamed to `UploadTask` (extends `Task`).
    - **BREAKING**: `StorageDownloadTask` has been renamed to `DownloadTask` (extends `Task`).
    - **BREAKING**: `StorageTaskEvent` has been removed (see below).
    - **BREAKING**: `StorageTaskSnapshot` has been renamed to `TaskSnapshot`.
    - **BREAKING**: `pause()`, `cancel()` and `resume()` are now Futures which return a boolean value to represent whether the status update was successful.
      - Previously, these were `void` methods but still carried out an asynchronous tasks, potentially leading to uncaught exceptions.
    - **BREAKING**: `isCanceled`, `isComplete`, `isInProgress`, `isPaused` and `isSuccessful` have now been removed. Instead, you should subscribe to the stream (for paused/progress/complete/error events) or the task `Future` for task completion/errors.
     - Additionally the latest `TaskSnapshot` now provides the latest `TaskState` via `task.snapshot.state`.
    - **BREAKING**: The `events` stream (now `snapshotEvents`) previously returned a `StorageTaskEvent`, containing a `StorageTaskEventType` and `StorageTaskSnapshot` Instead, the stream now returns a `TaskSnapshot` which includes the `state`.
    - **BREAKING**: A task failure and cancellation now throw a `FirebaseException` instead of a new event.
    - **DEPRECATED**: `events` stream is deprecated in favor of `snapshotEvents`.
    - **DEPRECATED**: `lastSnapshot` is deprecated in favor of `snapshot`.

#### Example

The new Tasks API matches the Web SDK API, for example:

```dart
UploadTask task = FirebaseStorage.instance.ref('/notes.text').putString('My notes!');

// Optional
task.snapshotEvents.listen((TaskSnapshot snapshot) {
  print('Snapshot state: ${snapshot.state}'); // paused, running, complete
  print('Progress: ${snapshot.totalBytes / snapshot.bytesTransferred}');
}, onError: (Object e) {
  print(e); // FirebaseException
});

// Optional
task
  .then((TaskSnapshot snapshot) {
    print('Upload complete!');
  })
  .catchError((Object e) {
    print(e); // FirebaseException
  });
```

Subscribing to Stream updates and/or the tasks delegating Future is optional - if you require progress updates on your task use the Stream, otherwise
the Future will resolve once its complete. Using both together is also supported.

## 4.0.0

* Depend on `firebase_core`.
* Firebase iOS SDK versions are now locked to use the same version defined in `firebase_core`.
* Firebase Android SDK versions are now using the Firebase Bill of Materials (BoM) to specify individual SDK versions. BoM version is also sourced from `firebase_core`.
* Allow iOS & MacOS plugins to be imported as modules.

## 3.1.6

* Update lower bound of dart dependency to 2.0.0.

## 3.1.5

* Add macOS support

## 3.1.4

* Fix for missing UserAgent.h compilation failures.

## 3.1.3

* Replace deprecated `getFlutterEngine` call on Android.

## 3.1.2

* Make the pedantic dev_dependency explicit.

## 3.1.1

* Removed unnecessary debug print statements ("i am working").

## 3.1.0

* Added error handling to `StorageFileDownloadTask` and added propagation of errors to the Future returned by the `writeToFile` method in `StorageReference`.
* Added unit tests for writeToFile.
* Updated integration test in example to use proper error handling.

## 3.0.11

* Remove the deprecated `author:` field from pubspec.yaml
* Migrate the plugin to the pubspec platforms manifest.
* Bump the minimum Flutter version to 1.10.0.

## 3.0.10

* Fix example app by adding a call to `ensureInitialized`.

## 3.0.9

* Support the v2 Android embedding.

## 3.0.8

* Updated README instructions for contributing for consistency with other Flutterfire plugins.

## 3.0.7

* Remove AndroidX warning.

## 3.0.6

* Update documentation to reflect new repository location.
* Update unit tests to call `TestWidgetsFlutterBinding.ensureInitialized`.
* Remove executable bit on LICENSE file.

## 3.0.5

* Removed automatic print statements for `StorageTaskEvent`'s.
  If you want to see the event status in your logs now, you will have to use the following:
  `storageReference.put{File/Data}(..).events.listen((event) => print('EVENT ${event.type}'));`
* Updated `README.md` to explain the above.

## 3.0.4

* Update google-services Android gradle plugin to 4.3.0 in documentation and examples.

## 3.0.3

* Fix inconsistency of `getPath`, on Android the path returned started with a `/` but on iOS it did not
* Fix content-type auto-detection on Android

## 3.0.2

* Automatically use version from pubspec.yaml when reporting usage to Firebase.

## 3.0.1

* Add missing template type parameter to `invokeMethod` calls.
* Bump minimum Flutter version to 1.5.0.
* Replace invokeMethod with invokeMapMethod wherever necessary.

## 3.0.0

* Update Android dependencies to latest.

## 2.1.1+2

* On iOS, use `putFile` instead of `putData` appropriately to detect `Content-Type`.

## 2.1.1+1

* On iOS, gracefully handle the case of uploading a nonexistent file without crashing.

## 2.1.1

* Added integration tests.

## 2.1.0+1

* Reverting error.code casting/formatting to what it was until version 2.0.1.

## 2.1.0

* Added support for getReferenceFromUrl.

## 2.0.1+2

* Log messages about automatic configuration of the default app are now less confusing.

## 2.0.1+1

* Remove categories.

## 2.0.1

* Log a more detailed warning at build time about the previous AndroidX
  migration.

## 2.0.0

* **Breaking change**. Migrate from the deprecated original Android Support
  Library to AndroidX. This shouldn't result in any functional changes, but it
  requires any Android apps using this plugin to [also
  migrate](https://developer.android.com/jetpack/androidx/migrate) if they're
  using the original support library.

  This was originally incorrectly pushed in the `1.1.0` update.

## 1.1.0+1

* **Revert the breaking 1.1.0 update**. 1.1.0 was known to be breaking and
  should have incremented the major version number instead of the minor. This
  revert is in and of itself breaking for anyone that has already migrated
  however. Anyone who has already migrated their app to AndroidX should
  immediately update to `2.0.0` instead. That's the correctly versioned new push
  of `1.1.0`.

## 1.1.0

* **BAD**. This was a breaking change that was incorrectly published on a minor
  version upgrade, should never have happened. Reverted by 1.1.0+1.

* **Breaking change**. Migrate from the deprecated original Android Support
  Library to AndroidX. This shouldn't result in any functional changes, but it
  requires any Android apps using this plugin to [also
  migrate](https://developer.android.com/jetpack/androidx/migrate) if they're
  using the original support library.

## 1.0.4

* Bump Android dependencies to latest.

## 1.0.3

* Added monitoring of StorageUploadTask via `events` stream.
* Added support for StorageUploadTask functions: `pause`, `resume`, `cancel`.
* Set http version to be compatible with flutter_test.

## 1.0.2

* Added missing http package dependency.

## 1.0.1

* Bump Android and Firebase dependency versions.

## 1.0.0

* **Breaking change**. Make StorageUploadTask implementation classes private.
* Bump to released version

## 0.3.7

* Updated Gradle tooling to match Android Studio 3.1.2.

## 0.3.6

* Added support for custom metadata.

## 0.3.5

* Updated iOS implementation to reflect Firebase API changes.

## 0.3.4

* Added timeout properties to FirebaseStorage.

## 0.3.3

* Added support for initialization with a custom Firebase app.

## 0.3.2

* Added support for StorageReference `writeToFile`.

## 0.3.1

* Added support for StorageReference functions: `getParent`, `getRoot`, `getStorage`, `getName`, `getPath`, `getBucket`.

## 0.3.0

* **Breaking change**. Changed StorageUploadTask to abstract, removed the 'file' field, and made 'path' and 'metadata'
  private. Added two subclasses: StorageFileUploadTask and StorageDataUploadTask.
* Deprecated the `put` function and added `putFile` and `putData` to upload files and bytes respectively.

## 0.2.6

* Added support for updateMetadata.

## 0.2.5

* Added StorageMetadata class, support for getMetadata, and support for uploading file with metadata.

## 0.2.4

* Updated Google Play Services dependencies to version 15.0.0.

## 0.2.3

* Updated package channel name and made channel visible for testing

## 0.2.2

* Simplified podspec for Cocoapods 1.5.0, avoiding link issues in app archives.

## 0.2.1

* Added support for getDownloadUrl.

## 0.2.0

* **Breaking change**. Set SDK constraints to match the Flutter beta release.

## 0.1.5

* Fix Dart 2 type errors.

## 0.1.4

* Enabled use in Swift projects.

## 0.1.3

* Added StorageReference `path` getter to retrieve the path component for the storage node.

## 0.1.2

* Added StorageReference delete function to remove files from Firebase.

## 0.1.1

* Simplified and upgraded Android project template to Android SDK 27.
* Updated package description.

## 0.1.0

* **Breaking change**. Upgraded to Gradle 4.1 and Android Studio Gradle plugin
  3.0.1. Older Flutter projects need to upgrade their Gradle setup as well in
  order to use this version of the plugin. Instructions can be found
  [here](https://github.com/flutter/flutter/wiki/Updating-Flutter-projects-to-Gradle-4.1-and-Android-Studio-Gradle-plugin-3.0.1).
* Relaxed GMS dependency to [11.4.0,12.0[

## 0.0.8

* Added FLT prefix to iOS types
* Change GMS dependency to 11.4.+

## 0.0.7

* Change GMS dependency to 11.+

## 0.0.6

* Added StorageReference getData function to download files into memory.

## 0.0.5+1

* Aligned author name with rest of repo.

## 0.0.5

* Updated to Firebase SDK to always use latest patch version for 11.0.x builds
* Fix crash when encountering upload failure

## 0.0.4

* Updated to Firebase SDK Version 11.0.1

## 0.0.3

* Suppress unchecked warnings

## 0.0.2

* Bumped buildToolsVersion to 25.0.3
* Updated README

## 0.0.1

* Initial Release
