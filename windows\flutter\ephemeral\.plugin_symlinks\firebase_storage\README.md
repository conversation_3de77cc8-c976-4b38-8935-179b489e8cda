[<img src="https://raw.githubusercontent.com/firebase/flutterfire/main/.github/images/flutter_favorite.png" width="200" />](https://flutter.dev/docs/development/packages-and-plugins/favorites)

# Cloud Storage for Flutter
[![pub package](https://img.shields.io/pub/v/firebase_storage.svg)](https://pub.dev/packages/firebase_storage)

A Flutter plugin to use the [Firebase Cloud Storage API](https://firebase.google.com/docs/storage/).

To learn more about Storage, please visit the [Firebase website](https://firebase.google.com/products/storage)

## Getting Started

To get started with Cloud Storage for Flutter, please [see the documentation](https://firebase.google.com/docs/storage/flutter/start).

## Usage

To use this plugin, please visit the [Storage Usage documentation](https://firebase.google.com/docs/storage/flutter/create-reference)

## Issues and feedback

Please file FlutterFire specific issues, bugs, or feature requests in our [issue tracker](https://github.com/firebase/flutterfire/issues/new).

Plugin issues that are not specific to FlutterFire can be filed in the [Flutter issue tracker](https://github.com/flutter/flutter/issues/new).

To contribute a change to this plugin,
please review our [contribution guide](https://github.com/firebase/flutterfire/blob/main/CONTRIBUTING.md)
and open a [pull request](https://github.com/firebase/flutterfire/pulls).
