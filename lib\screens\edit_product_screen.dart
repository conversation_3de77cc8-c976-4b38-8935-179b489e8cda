import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../services/product_service.dart';
import '../services/image_service.dart';
import '../widgets/product_dashboard/product_form_widgets.dart';

class EditProductScreen extends StatefulWidget {
  final ProductModel product;

  const EditProductScreen({
    super.key,
    required this.product,
  });

  @override
  State<EditProductScreen> createState() => _EditProductScreenState();
}

class _EditProductScreenState extends State<EditProductScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _priceController;
  late TextEditingController _originalPriceController;
  late TextEditingController _stockController;
  late TextEditingController _whatsAppController;
  late TextEditingController _weChatController;


  late String _selectedCategory;
  List<XFile> _newImages = [];
  List<String> _existingImageUrls = [];
  late List<String> _tags;
  late bool _isAvailable;
  late bool _isFeatured;
  bool _isLoading = false;

  final List<String> _categories = [
    'Electronics',
    'Fashion',
    'Furniture',
    'Sports',
    'Books',
    'Beauty',
    'Automotive',
    'Food & Beverages',
    'Health & Wellness',
    'Home & Garden',
    'Toys & Games',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.product.name);
    _descriptionController = TextEditingController(text: widget.product.description);
    _priceController = TextEditingController(text: widget.product.price.toString());
    _originalPriceController = TextEditingController(
      text: widget.product.originalPrice?.toString() ?? '',
    );
    _stockController = TextEditingController(text: widget.product.stockQuantity.toString());
    _whatsAppController = TextEditingController(text: widget.product.sellerWhatsApp ?? '');
    _weChatController = TextEditingController(text: widget.product.sellerWeChat ?? '');


    _selectedCategory = widget.product.category;
    _existingImageUrls = List.from(widget.product.imageUrls);
    _tags = List.from(widget.product.tags);
    _isAvailable = widget.product.isAvailable;
    _isFeatured = widget.product.isFeatured;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _stockController.dispose();
    _whatsAppController.dispose();
    _weChatController.dispose();

    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final totalImages = _existingImageUrls.length + _newImages.length;
      final remainingSlots = 5 - totalImages;

      if (remainingSlots <= 0) {
        _showErrorSnackBar('Maximum 5 images allowed');
        return;
      }

      final images = await ImageService.pickMultipleImages(maxImages: remainingSlots);
      if (images != null && images.isNotEmpty) {
        setState(() {
          _newImages.addAll(images);
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error selecting images: $e');
    }
  }

  void _removeNewImage(int index) {
    setState(() {
      _newImages.removeAt(index);
    });
  }

  void _removeExistingImage(int index) {
    setState(() {
      _existingImageUrls.removeAt(index);
    });
  }

  void _addTag(String tag) {
    if (tag.isNotEmpty && !_tags.contains(tag.toLowerCase())) {
      setState(() {
        _tags.add(tag.toLowerCase());
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  Future<void> _updateProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final totalImages = _existingImageUrls.length + _newImages.length;
    if (totalImages == 0) {
      _showErrorSnackBar('Please add at least one product image');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      List<String> finalImageUrls = List.from(_existingImageUrls);

      // Upload new images if any
      if (_newImages.isNotEmpty) {
        final newImageUrls = await ImageService.uploadProductImages(
          imageFiles: _newImages,
          productId: widget.product.id,
        );
        finalImageUrls.addAll(newImageUrls);
      }

      if (finalImageUrls.isEmpty) {
        throw Exception('No images available for the product');
      }

      // Create updated product model
      final updatedProduct = widget.product.copyWith(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text),
        originalPrice: _originalPriceController.text.isNotEmpty
            ? double.parse(_originalPriceController.text)
            : null,
        category: _selectedCategory,
        imageUrls: finalImageUrls,
        tags: _tags,
        stockQuantity: _stockController.text.trim().isNotEmpty
            ? int.parse(_stockController.text)
            : 999999, // Large number for unlimited stock
        isAvailable: _isAvailable,
        isFeatured: _isFeatured,
        updatedAt: DateTime.now(),
        sellerWhatsApp: _whatsAppController.text.trim().isNotEmpty
            ? _whatsAppController.text.trim()
            : null,
        sellerWeChat: _weChatController.text.trim().isNotEmpty
            ? _weChatController.text.trim()
            : null,
        sellerLocation: null,
      );

      // Update in Firestore
      final success = await ProductService.updateProduct(updatedProduct);

      if (success) {
        if (mounted) {
          Navigator.of(context).pop(updatedProduct);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product updated successfully!'),
              backgroundColor: AppConstants.successColor,
            ),
          );
        }
      } else {
        throw Exception('Failed to update product');
      }
    } catch (e) {
      _showErrorSnackBar('Error updating product: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Edit Product',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        backgroundColor: AppConstants.surfaceColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppConstants.textPrimaryColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _updateProduct,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppConstants.primaryColor,
                    ),
                  )
                : const Text(
                    'Update',
                    style: TextStyle(
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Images Section
              EditProductImagePicker(
                existingImages: _existingImageUrls,
                newImages: _newImages,
                onPickImages: _pickImages,
                onRemoveNewImage: _removeNewImage,
                onRemoveExistingImage: _removeExistingImage,
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Basic Information
              _buildSectionTitle('Basic Information'),
              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _nameController,
                label: 'Product Name',
                hint: 'Enter product name',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Product name is required';
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _descriptionController,
                label: 'Description',
                hint: 'Describe your product',
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Description is required';
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              CategoryDropdown(
                value: _selectedCategory,
                categories: _categories,
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value!;
                  });
                },
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Pricing Section
              _buildSectionTitle('Pricing'),
              const SizedBox(height: AppConstants.paddingMedium),

              Row(
                children: [
                  Expanded(
                    child: ProductFormField(
                      controller: _priceController,
                      label: 'Price (\$)',
                      hint: '0.00',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Price is required';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Enter valid price';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: ProductFormField(
                      controller: _originalPriceController,
                      label: 'Original Price (\$)',
                      hint: '0.00 (optional)',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'Enter valid price';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _stockController,
                label: 'Stock Quantity (Optional)',
                hint: 'Enter stock quantity (leave empty for unlimited)',
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    if (int.tryParse(value) == null) {
                      return 'Enter valid quantity';
                    }
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Tags Section
              _buildSectionTitle('Tags'),
              const SizedBox(height: AppConstants.paddingMedium),

              TagInput(
                tags: _tags,
                onAddTag: _addTag,
                onRemoveTag: _removeTag,
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Contact Information
              _buildSectionTitle('Contact Information (Optional)'),
              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _whatsAppController,
                label: 'WhatsApp Number',
                hint: '+1234567890',
                keyboardType: TextInputType.phone,
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _weChatController,
                label: 'WeChat ID',
                hint: 'Your WeChat ID',
              ),



              const SizedBox(height: AppConstants.paddingLarge),

              // Settings Section
              _buildSectionTitle('Settings'),
              const SizedBox(height: AppConstants.paddingMedium),

              SwitchListTile(
                title: const Text('Available for Sale'),
                subtitle: const Text('Make this product visible to buyers'),
                value: _isAvailable,
                onChanged: (value) {
                  setState(() {
                    _isAvailable = value;
                  });
                },
                activeColor: AppConstants.primaryColor,
              ),

              SwitchListTile(
                title: const Text('Featured Product'),
                subtitle: const Text('Highlight this product in search results'),
                value: _isFeatured,
                onChanged: (value) {
                  setState(() {
                    _isFeatured = value;
                  });
                },
                activeColor: AppConstants.primaryColor,
              ),

              const SizedBox(height: AppConstants.paddingXLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: AppConstants.fontSizeLarge,
        fontWeight: FontWeight.w600,
        color: AppConstants.textPrimaryColor,
      ),
    );
  }
}

class EditProductImagePicker extends StatelessWidget {
  final List<String> existingImages;
  final List<XFile> newImages;
  final VoidCallback onPickImages;
  final Function(int) onRemoveNewImage;
  final Function(int) onRemoveExistingImage;

  const EditProductImagePicker({
    super.key,
    required this.existingImages,
    required this.newImages,
    required this.onPickImages,
    required this.onRemoveNewImage,
    required this.onRemoveExistingImage,
  });

  @override
  Widget build(BuildContext context) {
    final totalImages = existingImages.length + newImages.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Product Images',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.w600,
                color: AppConstants.textPrimaryColor,
              ),
            ),
            Text(
              '$totalImages/5',
              style: const TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),

        // Image Grid
        if (totalImages > 0)
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: totalImages + (totalImages < 5 ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == totalImages) {
                return _buildAddImageButton();
              }

              if (index < existingImages.length) {
                return _buildExistingImageItem(existingImages[index], index);
              } else {
                final newImageIndex = index - existingImages.length;
                return _buildNewImageItem(newImages[newImageIndex], newImageIndex);
              }
            },
          )
        else
          _buildAddImageButton(),
      ],
    );
  }

  Widget _buildExistingImageItem(String imageUrl, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            image: DecorationImage(
              image: NetworkImage(imageUrl),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => onRemoveExistingImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: AppConstants.errorColor,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNewImageItem(XFile image, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            border: Border.all(
              color: AppConstants.primaryColor.withOpacity(0.5),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            child: _buildImageWidget(image),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => onRemoveNewImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: AppConstants.errorColor,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
        // New image indicator
        Positioned(
          bottom: 4,
          left: 4,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: AppConstants.primaryColor,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: const Text(
              'NEW',
              style: TextStyle(
                color: Colors.white,
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: onPickImages,
      child: Container(
        decoration: BoxDecoration(
          color: AppConstants.backgroundColor,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          border: Border.all(
            color: AppConstants.primaryColor.withOpacity(0.3),
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              color: AppConstants.primaryColor,
              size: 32,
            ),
            SizedBox(height: 4),
            Text(
              'Add Image',
              style: TextStyle(
                color: AppConstants.primaryColor,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageWidget(XFile image) {
    if (kIsWeb) {
      // For web, use FutureBuilder with readAsBytes
      return FutureBuilder<Uint8List>(
        future: image.readAsBytes(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              color: AppConstants.backgroundColor,
              child: const Center(
                child: CircularProgressIndicator(
                  color: AppConstants.primaryColor,
                  strokeWidth: 2,
                ),
              ),
            );
          }

          if (snapshot.hasError || !snapshot.hasData) {
            return Container(
              color: AppConstants.backgroundColor,
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.broken_image,
                    color: AppConstants.errorColor,
                    size: 32,
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Load Error',
                    style: TextStyle(
                      color: AppConstants.errorColor,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            );
          }

          return Image.memory(
            snapshot.data!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          );
        },
      );
    } else {
      // For mobile, use Image.file
      return Image.file(
        File(image.path),
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: AppConstants.backgroundColor,
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image,
                  color: AppConstants.errorColor,
                  size: 32,
                ),
                SizedBox(height: 4),
                Text(
                  'Load Error',
                  style: TextStyle(
                    color: AppConstants.errorColor,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
  }
}