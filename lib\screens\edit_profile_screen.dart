import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../models/user_model.dart';
import '../services/image_service.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({Key? key}) : super(key: key);

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _displayNameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _bioController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();

  String? _selectedGender;
  String? _selectedCountry;
  bool _isLoading = false;

  // Gender options
  final List<String> _genderOptions = [
    'Male',
    'Female',
    'Other',
    'Prefer not to say',
  ];

  // Country options (common countries)
  final List<String> _countryOptions = [
    'Bangladesh',
    'India',
    'Pakistan',
    'United States',
    'United Kingdom',
    'Canada',
    'Australia',
    'Germany',
    'France',
    'Japan',
    'China',
    'South Korea',
    'Singapore',
    'Malaysia',
    'Thailand',
    'Indonesia',
    'Philippines',
    'Vietnam',
    'Saudi Arabia',
    'UAE',
    'Qatar',
    'Kuwait',
    'Turkey',
    'Egypt',
    'South Africa',
    'Nigeria',
    'Kenya',
    'Brazil',
    'Argentina',
    'Mexico',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _setupListeners();
  }

  void _setupListeners() {
    _displayNameController.addListener(() => setState(() {}));
    _usernameController.addListener(() => setState(() {}));
    _bioController.addListener(() => setState(() {}));
    _addressController.addListener(() => setState(() {}));
  }

  bool get _hasChanges {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user == null) return false;

    return _displayNameController.text.trim() != user.displayName ||
           _usernameController.text.trim() != user.username ||
           _bioController.text.trim() != (user.bio ?? '') ||
           _selectedGender != user.gender ||
           _addressController.text.trim() != (user.address ?? '') ||
           _selectedCountry != user.country;
  }

  void _loadUserData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user != null) {
      _displayNameController.text = user.displayName;
      _usernameController.text = user.username;
      _bioController.text = user.bio ?? '';
      _emailController.text = user.email;
      _addressController.text = user.address ?? '';
      _selectedGender = user.gender;
      _selectedCountry = user.country;
    }
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
      appBar: AppBar(
        title: const Text('Edit Profile'),
        backgroundColor: AppConstants.surfaceColor,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: (_isLoading || !_hasChanges) ? null : _saveProfile,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(
                    'Save',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: _hasChanges ? AppConstants.primaryColor : AppConstants.textHintColor,
                    ),
                  ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: IgnorePointer(
          ignoring: _isLoading,
          child: Form(
            key: _formKey,
            child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Cover Photo Section
              const Text(
                'Cover Photo',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.textPrimaryColor,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              _buildCoverPhotoSection(),

              const SizedBox(height: AppConstants.paddingLarge),

              // Profile Picture Section
              Column(
                children: [
                  Center(
                    child: Stack(
                      children: [
                        Consumer<AuthProvider>(
                          builder: (context, authProvider, child) {
                            final user = authProvider.currentUser;
                            return Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                  width: 3,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 10,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(17),
                                child: user?.profileImageUrl != null
                                    ? Image.network(
                                        user!.profileImageUrl!,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) {
                                          return Container(
                                            color: AppConstants.primaryColor,
                                            child: const Icon(
                                              Icons.person,
                                              size: 50,
                                              color: AppConstants.onPrimaryColor,
                                            ),
                                          );
                                        },
                                      )
                                    : Container(
                                        color: AppConstants.primaryColor,
                                        child: const Icon(
                                          Icons.person,
                                          size: 50,
                                          color: AppConstants.onPrimaryColor,
                                        ),
                                      ),
                              ),
                            );
                          },
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppConstants.primaryColor,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white,
                                width: 2,
                              ),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(10),
                                onTap: _changeProfilePicture,
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  child: const Icon(
                                    Icons.camera_alt,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),


                ],
              ),

              const SizedBox(height: AppConstants.paddingXLarge),

              // Form Fields
              _buildSectionTitle('Personal Information'),
              const SizedBox(height: AppConstants.paddingMedium),

              // Display Name Field
              _buildTextField(
                controller: _displayNameController,
                label: 'Display Name',
                icon: Icons.person_outline,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Display name is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Display name must be at least 2 characters';
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // Username Field
              _buildTextField(
                controller: _usernameController,
                label: 'Username',
                icon: Icons.alternate_email,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Username is required';
                  }
                  if (value.trim().length < 3) {
                    return 'Username must be at least 3 characters';
                  }
                  if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value.trim())) {
                    return 'Username can only contain letters, numbers, and underscores';
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // Email Field (Read-only)
              _buildTextField(
                controller: _emailController,
                label: 'Email',
                icon: Icons.email_outlined,
                readOnly: true,
                suffixIcon: const Icon(
                  Icons.lock_outline,
                  color: AppConstants.textHintColor,
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Additional Information Section
              _buildSectionTitle('Additional Information'),
              const SizedBox(height: AppConstants.paddingMedium),

              // Gender Dropdown
              _buildGenderDropdown(),
              _buildHelpText('This information helps us provide better recommendations'),

              const SizedBox(height: AppConstants.paddingMedium),

              // Address Field
              _buildTextField(
                controller: _addressController,
                label: 'Address',
                icon: Icons.location_on_outlined,
                maxLines: 2,
              ),
              _buildHelpText('Your address will be used for delivery purposes'),

              const SizedBox(height: AppConstants.paddingMedium),

              // Country Dropdown
              _buildCountryDropdown(),
              _buildHelpText('Select your country of residence'),

              const SizedBox(height: AppConstants.paddingLarge),

              // Biography Section
              _buildSectionTitle('Biography'),
              const SizedBox(height: AppConstants.paddingMedium),

              _buildBioField(),

              const SizedBox(height: AppConstants.paddingXLarge),
            ],
            ),
          ),
        ),
      ),
    ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: AppConstants.primaryColor,
      ),
    );
  }

  Widget _buildHelpText(String text) {
    return Padding(
      padding: const EdgeInsets.only(top: 4, left: 12),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppConstants.textHintColor,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    bool readOnly = false,
    Widget? suffixIcon,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      readOnly: readOnly,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          borderSide: BorderSide(
            color: AppConstants.primaryColor,
            width: 2,
          ),
        ),
        filled: readOnly,
        fillColor: readOnly ? AppConstants.backgroundColor : null,
      ),
    );
  }

  Widget _buildBioField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _bioController,
          maxLines: 4,
          maxLength: 200,
          decoration: InputDecoration(
            labelText: 'Biography',
            hintText: 'Tell people about yourself, your interests, and what makes you unique...',
            prefixIcon: const Icon(Icons.info_outline),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: BorderSide(
                color: AppConstants.primaryColor,
                width: 2,
              ),
            ),
            alignLabelWithHint: true,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Your biography will be visible to all users visiting your profile.',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppConstants.textHintColor,
          ),
        ),
      ],
    );
  }

  Widget _buildGenderDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedGender,
      decoration: InputDecoration(
        labelText: 'Gender',
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          borderSide: BorderSide(
            color: AppConstants.primaryColor,
            width: 2,
          ),
        ),
      ),
      items: _genderOptions.map((String gender) {
        return DropdownMenuItem<String>(
          value: gender,
          child: Text(gender),
        );
      }).toList(),
      onChanged: (String? newValue) {
        setState(() {
          _selectedGender = newValue;
        });
      },
      hint: const Text('Select your gender'),
    );
  }

  Widget _buildCountryDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCountry,
      decoration: InputDecoration(
        labelText: 'Country',
        prefixIcon: const Icon(Icons.flag_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          borderSide: BorderSide(
            color: AppConstants.primaryColor,
            width: 2,
          ),
        ),
      ),
      items: _countryOptions.map((String country) {
        return DropdownMenuItem<String>(
          value: country,
          child: Text(country),
        );
      }).toList(),
      onChanged: (String? newValue) {
        setState(() {
          _selectedCountry = newValue;
        });
      },
      hint: const Text('Select your country'),
      isExpanded: true,
    );
  }



  void _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser != null) {
        // Update user profile using AuthProvider
        final success = await authProvider.updateProfile(
          displayName: _displayNameController.text.trim(),
          username: _usernameController.text.trim(),
          bio: _bioController.text.trim().isEmpty ? null : _bioController.text.trim(),
          gender: _selectedGender,
          address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
          country: _selectedCountry,
        );

        if (!success) {
          throw Exception(authProvider.errorMessage ?? 'Failed to update profile');
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                const Text('Profile updated successfully!'),
              ],
            ),
            backgroundColor: AppConstants.successColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Failed to update profile';

        // Parse specific error messages
        if (e.toString().contains('Username is already taken')) {
          errorMessage = 'This username is already taken. Please choose a different one.';
        } else if (e.toString().contains('network')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (e.toString().contains('permission')) {
          errorMessage = 'Permission denied. Please try logging in again.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: AppConstants.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _changeProfilePicture() async {
    try {
      print('EditProfileScreen: Starting profile picture change');

      // Show image picker options
      final XFile? imageFile = await _showImagePickerDialog();
      print('EditProfileScreen: Image file selected: ${imageFile?.path}');

      if (imageFile != null) {
        setState(() {
          _isLoading = true;
        });

        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final currentUser = authProvider.currentUser;
        print('EditProfileScreen: Current user ID: ${currentUser?.id}');

        if (currentUser != null) {
          print('EditProfileScreen: Starting image upload to Cloudinary');

          // Upload image to Cloudinary
          final imageUrl = await ImageService.uploadProfileImage(
            imageFile: imageFile,
            userId: currentUser.id,
          );
          print('EditProfileScreen: Upload result: $imageUrl');

          if (imageUrl != null) {
            print('EditProfileScreen: Updating profile in Firestore');

            // Update user in Firestore
            final success = await authProvider.updateProfile(
              profileImageUrl: imageUrl,
            );

            print('EditProfileScreen: Profile update success: $success');

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      const Text('Profile picture updated successfully!'),
                    ],
                  ),
                  backgroundColor: AppConstants.successColor,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            }
          } else {
            print('EditProfileScreen: Image upload failed');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.white),
                      const SizedBox(width: 8),
                      const Text('Failed to upload profile picture. Please try again.'),
                    ],
                  ),
                  backgroundColor: AppConstants.errorColor,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            }
          }
        } else {
          print('EditProfileScreen: No current user found');
        }
      } else {
        print('EditProfileScreen: No image file selected');
      }
    } catch (e) {
      print('EditProfileScreen: Error changing profile picture: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Error: ${e.toString()}')),
              ],
            ),
            backgroundColor: AppConstants.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _changeCoverPhoto() async {
    try {
      print('EditProfileScreen: Starting cover photo change');

      // Show image picker options
      final XFile? imageFile = await _showImagePickerDialog();
      print('EditProfileScreen: Cover image file selected: ${imageFile?.path}');

      if (imageFile != null) {
        setState(() {
          _isLoading = true;
        });

        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final currentUser = authProvider.currentUser;
        print('EditProfileScreen: Current user ID for cover: ${currentUser?.id}');

        if (currentUser != null) {
          print('EditProfileScreen: Starting cover image upload to Cloudinary');

          // Upload image to Cloudinary
          final imageUrl = await ImageService.uploadCoverImage(
            imageFile: imageFile,
            userId: currentUser.id,
          );
          print('EditProfileScreen: Cover upload result: $imageUrl');

          if (imageUrl != null) {
            print('EditProfileScreen: Updating cover photo in Firestore');

            // Update user in Firestore
            final success = await authProvider.updateProfile(
              coverImageUrl: imageUrl,
            );

            print('EditProfileScreen: Cover photo update success: $success');

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      const Text('Cover photo updated successfully!'),
                    ],
                  ),
                  backgroundColor: AppConstants.successColor,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            }
          } else {
            print('EditProfileScreen: Cover image upload failed');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.white),
                      const SizedBox(width: 8),
                      const Text('Failed to upload cover photo. Please try again.'),
                    ],
                  ),
                  backgroundColor: AppConstants.errorColor,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            }
          }
        } else {
          print('EditProfileScreen: No current user found for cover photo');
        }
      } else {
        print('EditProfileScreen: No cover image file selected');
      }
    } catch (e) {
      print('EditProfileScreen: Error changing cover photo: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Error: ${e.toString()}')),
              ],
            ),
            backgroundColor: AppConstants.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }



  Future<XFile?> _showImagePickerDialog() async {
    return await showModalBottomSheet<XFile?>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'Select Image Source',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildImageSourceOption(
                    icon: Icons.photo_library,
                    label: 'Gallery',
                    onTap: () async {
                      try {
                        print('EditProfileScreen: Gallery button tapped');
                        final image = await ImageService.pickImageFromGallery();
                        print('EditProfileScreen: Gallery picker result: ${image?.path}');
                        Navigator.pop(context, image);
                      } catch (e) {
                        print('EditProfileScreen: Error in gallery picker: $e');
                        Navigator.pop(context, null);
                      }
                    },
                  ),
                  _buildImageSourceOption(
                    icon: Icons.camera_alt,
                    label: 'Camera',
                    onTap: () async {
                      final image = await ImageService.pickImageFromCamera();
                      Navigator.pop(context, image);
                    },
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  Widget _buildImageSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppConstants.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: AppConstants.primaryColor.withOpacity(0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 40,
              color: AppConstants.primaryColor,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: AppConstants.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _onWillPop() async {
    // Check if any field has been modified
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user != null) {
      final hasChanges = _displayNameController.text.trim() != user.displayName ||
                        _usernameController.text.trim() != user.username ||
                        _bioController.text.trim() != (user.bio ?? '') ||
                        _selectedGender != user.gender ||
                        _addressController.text.trim() != (user.address ?? '') ||
                        _selectedCountry != user.country;

      if (hasChanges) {
        final shouldDiscard = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Discard Changes?'),
              content: const Text('You have unsaved changes. Are you sure you want to discard them?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: TextButton.styleFrom(
                    foregroundColor: AppConstants.errorColor,
                  ),
                  child: const Text('Discard'),
                ),
              ],
            );
          },
        );
        return shouldDiscard ?? false;
      }
    }
    return true;
  }

  void _showComingSoonDialog(String feature) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Coming Soon'),
          content: Text('$feature feature will be available soon!'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCoverPhotoSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        return Container(
          height: 220,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
            border: Border.all(
              color: AppConstants.primaryColor.withOpacity(0.2),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryColor.withOpacity(0.1),
                blurRadius: 15,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge - 2),
            child: Stack(
              children: [
                // Cover Photo or Default Background
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    gradient: user?.coverImageUrl != null
                        ? null
                        : LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppConstants.primaryColor.withOpacity(0.8),
                              AppConstants.primaryColor.withOpacity(0.6),
                              AppConstants.secondaryColor.withOpacity(0.4),
                            ],
                          ),
                  ),
                  child: user?.coverImageUrl != null
                      ? Image.network(
                          user!.coverImageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    AppConstants.primaryColor.withOpacity(0.8),
                                    AppConstants.secondaryColor.withOpacity(0.6),
                                  ],
                                ),
                              ),
                              child: const Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  size: 50,
                                  color: AppConstants.onPrimaryColor,
                                ),
                              ),
                            );
                          },
                        )
                      : const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.landscape,
                                size: 60,
                                color: AppConstants.onPrimaryColor,
                              ),
                              SizedBox(height: AppConstants.paddingSmall),
                              Text(
                                'Cover Photo',
                                style: TextStyle(
                                  color: AppConstants.onPrimaryColor,
                                  fontSize: AppConstants.fontSizeLarge,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                ),

                // Edit Button
                Positioned(
                  bottom: AppConstants.paddingMedium,
                  right: AppConstants.paddingMedium,
                  child: GestureDetector(
                    onTap: _changeCoverPhoto,
                    child: Container(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppConstants.primaryColor,
                            AppConstants.primaryColor.withOpacity(0.8),
                          ],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppConstants.primaryColor.withOpacity(0.4),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                            spreadRadius: 0,
                          ),
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.camera_alt,
                        color: AppConstants.onPrimaryColor,
                        size: 22,
                      ),
                    ),
                  ),
                ),

                // Edit Label
                Positioned(
                  bottom: AppConstants.paddingMedium,
                  left: AppConstants.paddingMedium,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                      vertical: AppConstants.paddingSmall,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                    ),
                    child: const Text(
                      'Tap to change cover photo',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: AppConstants.fontSizeSmall,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),

                // Loading Overlay
                if (_isLoading)
                  Container(
                    color: Colors.black.withOpacity(0.5),
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: AppConstants.onPrimaryColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
