import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_constants.dart';

class EnhancedBottomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final int? badgeCount;
  final Color? customColor;

  const EnhancedBottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    this.badgeCount,
    this.customColor,
  });
}

class EnhancedBottomNav extends StatefulWidget {
  final List<EnhancedBottomNavItem> items;
  final int currentIndex;
  final Function(int) onTap;
  final Color? backgroundColor;
  final double? height;

  const EnhancedBottomNav({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor,
    this.height,
  });

  @override
  State<EnhancedBottomNav> createState() => _EnhancedBottomNavState();
}

class _EnhancedBottomNavState extends State<EnhancedBottomNav>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _rippleController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rippleAnimation;

  int _lastTappedIndex = -1;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.animationDurationMedium,
      vsync: this,
    );
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rippleController.dispose();
    super.dispose();
  }

  void _handleTap(int index) {
    setState(() {
      _lastTappedIndex = index;
    });

    HapticFeedback.lightImpact();

    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    _rippleController.forward().then((_) {
      _rippleController.reset();
    });

    widget.onTap(index);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height ?? 80,
      margin: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? AppConstants.surfaceColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppConstants.borderRadiusXLarge),
          topRight: Radius.circular(AppConstants.borderRadiusXLarge),
        ),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryColor.withOpacity(0.15),
            blurRadius: 25,
            offset: const Offset(0, 10),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: AppConstants.primaryColor.withOpacity(0.1),
          width: 1.5,
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppConstants.borderRadiusXLarge),
          topRight: Radius.circular(AppConstants.borderRadiusXLarge),
        ),
        child: Stack(
          children: [
            // Glassmorphism background
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.9),
                    Colors.white.withOpacity(0.7),
                  ],
                ),
              ),
            ),

            // Active indicator background
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingXSmall,
                vertical: AppConstants.paddingXSmall,
              ),
              child: Row(
                children: List.generate(
                  widget.items.length,
                  (index) => Expanded(
                    child: AnimatedContainer(
                      duration: AppConstants.animationDurationMedium,
                      curve: Curves.easeInOut,
                      margin: const EdgeInsets.symmetric(
                        horizontal: 2,
                        vertical: 2,
                      ),
                      height: (widget.height ?? 80) - (AppConstants.paddingXSmall * 2) - 4,
                      decoration: BoxDecoration(
                        color: widget.currentIndex == index
                            ? AppConstants.primaryColor.withOpacity(0.12)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                        border: widget.currentIndex == index
                            ? Border.all(
                                color: AppConstants.primaryColor.withOpacity(0.2),
                                width: 1,
                              )
                            : null,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Navigation items
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingXSmall,
                vertical: AppConstants.paddingXSmall,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(
                  widget.items.length,
                  (index) => _buildNavItem(index),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(int index) {
    final isSelected = widget.currentIndex == index;
    final item = widget.items[index];
    final isLastTapped = _lastTappedIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: () => _handleTap(index),
        child: Container(
          padding: const EdgeInsets.symmetric(
            vertical: AppConstants.paddingXSmall,
            horizontal: AppConstants.paddingXSmall,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon with ripple effect and badge
              Stack(
                clipBehavior: Clip.none,
                children: [
                  AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: isSelected && isLastTapped ? _scaleAnimation.value : 1.0,
                        child: Stack(
                          children: [
                            // Ripple effect
                            if (isLastTapped)
                              AnimatedBuilder(
                                animation: _rippleAnimation,
                                builder: (context, child) {
                                  return Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: (item.customColor ?? AppConstants.primaryColor)
                                          .withOpacity(0.2 * (1 - _rippleAnimation.value)),
                                    ),
                                    transform: Matrix4.identity()
                                      ..scale(_rippleAnimation.value * 2),
                                  );
                                },
                              ),

                            // Icon container
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: isSelected
                                    ? (item.customColor ?? AppConstants.primaryColor).withOpacity(0.15)
                                    : Colors.transparent,
                              ),
                              child: Icon(
                                isSelected ? item.activeIcon : item.icon,
                                color: isSelected
                                    ? (item.customColor ?? AppConstants.primaryColor)
                                    : AppConstants.textSecondaryColor,
                                size: AppConstants.iconSizeSmall + 4,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),

                  // Badge
                  if (item.badgeCount != null && item.badgeCount! > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: _buildBadge(item.badgeCount!),
                    ),
                ],
              ),

              const SizedBox(height: 1),

              // Label with animation
              Flexible(
                child: AnimatedDefaultTextStyle(
                  duration: AppConstants.animationDurationMedium,
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeXSmall - 1,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    color: isSelected
                        ? (item.customColor ?? AppConstants.primaryColor)
                        : AppConstants.textSecondaryColor,
                  ),
                  child: Text(
                    item.label,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBadge(int count) {
    return AnimatedContainer(
      duration: AppConstants.animationDurationMedium,
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingXSmall + 1,
        vertical: AppConstants.paddingXSmall - 1,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.errorColor,
            AppConstants.errorColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        border: Border.all(
          color: AppConstants.surfaceColor,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppConstants.errorColor.withOpacity(0.4),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      constraints: const BoxConstraints(
        minWidth: 16,
        minHeight: 16,
      ),
      child: Text(
        count > 99 ? '99+' : count.toString(),
        style: const TextStyle(
          color: AppConstants.onErrorColor,
          fontSize: AppConstants.fontSizeXSmall - 1,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
