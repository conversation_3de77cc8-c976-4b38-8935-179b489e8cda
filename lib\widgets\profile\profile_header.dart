import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_constants.dart';
import '../../models/user_model.dart';

class ProfileHeader extends StatelessWidget {
  final UserModel? user;

  final VoidCallback? onProfilePhotoTap;

  const ProfileHeader({
    super.key,
    this.user,

    this.onProfilePhotoTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Cover Photo Section with Profile Picture Overlay
          _buildCoverWithProfileSection(context),

          // User Info Section
          _buildUserInfoSection(context),
        ],
      ),
    );
  }

  Widget _buildCoverWithProfileSection(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Cover Photo
        Container(
          height: 200,
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: user?.coverImageUrl != null
                  ? [
                      Colors.black.withOpacity(0.3),
                      Colors.transparent,
                    ]
                  : [
                      AppConstants.primaryColor,
                      AppConstants.primaryColor.withOpacity(0.8),
                      AppConstants.secondaryColor.withOpacity(0.6),
                    ],
            ),
            image: user?.coverImageUrl != null
                ? DecorationImage(
                    image: CachedNetworkImageProvider(user!.coverImageUrl!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: Stack(
            children: [
              // Decorative Elements
              _buildDecorativeElements(),
            ],
          ),
        ),

        // Profile Picture positioned at bottom of cover
        Positioned(
          bottom: -50,
          left: 20,
          child: _buildProfilePicture(context),
        ),
      ],
    );
  }

  Widget _buildUserInfoSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display Name with Verification Badge
          Row(
            children: [
              Expanded(
                child: Text(
                  user?.displayName ?? 'User Name',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppConstants.textPrimaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (user?.isVerified == true) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.verified,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 8),

          // Username
          Text(
            '@${user?.username ?? 'username'}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),

          const SizedBox(height: 12),

          // Bio (if available)
          if (user?.bio != null && user!.bio!.isNotEmpty) ...[
            Text(
              user!.bio!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConstants.textPrimaryColor,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Location and Joining Date
          Row(
            children: [
              if (user?.address != null || user?.country != null) ...[
                Icon(
                  Icons.location_on_outlined,
                  size: 16,
                  color: AppConstants.textSecondaryColor,
                ),
                const SizedBox(width: 4),
                Text(
                  _getLocationText(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
                const SizedBox(width: 16),
              ],
              Icon(
                Icons.calendar_today_outlined,
                size: 16,
                color: AppConstants.textSecondaryColor,
              ),
              const SizedBox(width: 4),
              Text(
                'Joined ${_formatJoiningDate(user?.createdAt)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppConstants.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDecorativeElements() {
    return Stack(
      children: [
        Positioned(
          top: 40,
          right: 60,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.1),
            ),
          ),
        ),
        Positioned(
          top: 80,
          left: 40,
          child: Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.05),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfilePicture(BuildContext context) {
    return Stack(
      children: [
        // Main profile picture
        GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            onProfilePhotoTap?.call();
          },
          child: Hero(
            tag: 'profile_photo_${user?.id ?? 'default'}',
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 4,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipOval(
                child: _buildProfileImage(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileImage() {
    if (user?.profileImageUrl != null) {
      return CachedNetworkImage(
        imageUrl: user!.profileImageUrl!,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppConstants.primaryColor.withOpacity(0.3),
                AppConstants.secondaryColor.withOpacity(0.3),
              ],
            ),
          ),
          child: const Center(
            child: CircularProgressIndicator(
              color: AppConstants.primaryColor,
            ),
          ),
        ),
        errorWidget: (context, url, error) => _buildDefaultAvatar(),
      );
    }
    return _buildDefaultAvatar();
  }

  Widget _buildDefaultAvatar() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryColor,
            AppConstants.secondaryColor,
          ],
        ),
      ),
      child: Center(
        child: Text(
          user?.displayName?.isNotEmpty == true
              ? user!.displayName[0].toUpperCase()
              : 'U',
          style: const TextStyle(
            fontSize: 50,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  String _getLocationText() {
    final locationParts = <String>[];

    if (user?.address != null && user!.address!.isNotEmpty) {
      locationParts.add(user!.address!);
    }

    if (user?.country != null && user!.country!.isNotEmpty) {
      locationParts.add(user!.country!);
    }

    return locationParts.join(', ');
  }

  String _formatJoiningDate(DateTime? date) {
    if (date == null) return 'Recently';

    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 30) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    }
  }
}
